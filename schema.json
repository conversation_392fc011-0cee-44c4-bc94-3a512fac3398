[{"name": "blockContent", "type": "type", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}}, {"name": "featureCardsSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "featureCardsSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "badge": {"type": "objectAttribute", "value": {"type": "inline", "name": "badge"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "featureCards": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"icon": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "iconBackgroundColor": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "primary"}, {"type": "string", "value": "success"}, {"type": "string", "value": "warning"}, {"type": "string", "value": "error"}, {"type": "string", "value": "info"}, {"type": "string", "value": "purple"}, {"type": "string", "value": "pink"}, {"type": "string", "value": "indigo"}, {"type": "string", "value": "gray"}]}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "featureCard"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "layout": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"columns": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "2"}, {"type": "string", "value": "3"}, {"type": "string", "value": "4"}]}, "optional": true}, "cardSpacing": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}]}, "optional": true}}}, "optional": true}, "textAlignment": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"desktop": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}, "mobile": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}}}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "inline", "name": "paddingControls"}, "optional": true}}}}, {"name": "pricingCalculatorSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "pricingCalculatorSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "badge": {"type": "objectAttribute", "value": {"type": "inline", "name": "badge"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "calculatorTitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "serviceSelectPlaceholder": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "optionSelectPlaceholder": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "vehicleInfoTitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "makeModelPlaceholder": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "mileagePlaceholder": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "estimateButtonText": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "totalLabel": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "discountLabel": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "vatLabel": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "inline", "name": "paddingControls"}, "optional": true}}}}, {"name": "faqSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "faqSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "faqCategories": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"categoryName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "questions": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"question": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "answer": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "featured": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "faqQuestion"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "faqCategory"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "inline", "name": "paddingControls"}, "optional": true}, "settings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"layout": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "sideBySide"}, {"type": "string", "value": "twoColumn"}, {"type": "string", "value": "singleColumn"}, {"type": "string", "value": "tabbed"}]}, "optional": true}, "showCategoryTabs": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "allowMultipleOpen": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "highlightFeatured": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "fullWidth": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}, "optional": true}}}}, {"name": "contactFormSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "contactFormSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "badge": {"type": "objectAttribute", "value": {"type": "inline", "name": "badge"}, "optional": true}, "features": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featureListItem"}}}, "optional": true}, "formHeading": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "formFields": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "placeholder": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "type": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "text"}, {"type": "string", "value": "email"}, {"type": "string", "value": "tel"}, {"type": "string", "value": "textarea"}]}, "optional": true}, "required": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "half"}, {"type": "string", "value": "full"}]}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "formField"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "submitButton": {"type": "objectAttribute", "value": {"type": "inline", "name": "ctaButton"}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "inline", "name": "paddingControls"}, "optional": true}, "settings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"layout": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "contentLeft"}, {"type": "string", "value": "contentRight"}]}, "optional": true}, "fullWidth": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}, "optional": true}}}}, {"name": "tryCarsuBanner", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "tryCarsuBanner"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "ctaButton": {"type": "objectAttribute", "value": {"type": "inline", "name": "ctaButton"}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "mainImage": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "glowImage": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "inline", "name": "paddingControls"}, "optional": true}, "settings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"fullWidth": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "borderRadius": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "sm"}, {"type": "string", "value": "md"}, {"type": "string", "value": "lg"}, {"type": "string", "value": "xl"}, {"type": "string", "value": "full"}]}, "optional": true}, "textAlignment": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"desktop": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}]}, "optional": true}, "mobile": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}]}, "optional": true}}}, "optional": true}, "imagePosition": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "right"}, {"type": "string", "value": "left"}]}, "optional": true}}}, "optional": true}}}}, {"name": "servicesSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "servicesSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "services": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "icon": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "check"}, {"type": "string", "value": "star"}, {"type": "string", "value": "arrowRight"}, {"type": "string", "value": "plus"}, {"type": "string", "value": "heart"}, {"type": "string", "value": "shield"}, {"type": "string", "value": "lightning"}, {"type": "string", "value": "globe"}, {"type": "string", "value": "cog"}, {"type": "string", "value": "user"}, {"type": "string", "value": "chat"}, {"type": "string", "value": "database"}, {"type": "string", "value": "book"}, {"type": "string", "value": "settings"}, {"type": "string", "value": "shield"}, {"type": "string", "value": "cloud"}, {"type": "string", "value": "phone"}, {"type": "string", "value": "email"}]}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "inline", "name": "color"}, "optional": true}, "backgroundImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "position": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "top-left"}, {"type": "string", "value": "top-center"}, {"type": "string", "value": "top-right"}, {"type": "string", "value": "center-left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "center-right"}, {"type": "string", "value": "bottom-left"}, {"type": "string", "value": "bottom-center"}, {"type": "string", "value": "bottom-right"}]}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "cover"}, {"type": "string", "value": "contain"}, {"type": "string", "value": "auto"}, {"type": "string", "value": "25%"}, {"type": "string", "value": "50%"}, {"type": "string", "value": "75%"}, {"type": "string", "value": "100%"}]}, "optional": true}, "opacity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "repeat": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "no-repeat"}, {"type": "string", "value": "repeat"}, {"type": "string", "value": "repeat-x"}, {"type": "string", "value": "repeat-y"}]}, "optional": true}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"top": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}}}, "optional": true}, "settings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"layout": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "grid-2"}, {"type": "string", "value": "grid-3"}, {"type": "string", "value": "grid-4"}, {"type": "string", "value": "list"}]}, "optional": true}, "textAlignment": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"desktop": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}, "mobile": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}}}, "optional": true}}}, "optional": true}}}}, {"name": "testimonialSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "testimonialSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "testimonials": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "testimonial", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "displaySettings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"itemsPerView": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"mobile": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "tablet": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "desktop": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}, "autoplay": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "autoplaySpeed": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "showDots": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "showArrows": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "infiniteLoop": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}, "optional": true}, "styling": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "textAlign": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "inline", "name": "paddingControls"}, "optional": true}}}, "optional": true}}}}, {"name": "featureSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "featureSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "layout": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "contentLeft"}, {"type": "string", "value": "contentRight"}, {"type": "string", "value": "contentCenter"}]}, "optional": true}, "badge": {"type": "objectAttribute", "value": {"type": "inline", "name": "badge"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "features": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featureListItem"}}}, "optional": true}, "subdescription": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "ctaButtons": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "ctaButton"}}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "inline", "name": "paddingControls"}, "optional": true}, "settings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"fullWidth": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "centerContent": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "imageAspectRatio": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "auto"}, {"type": "string", "value": "square"}, {"type": "string", "value": "landscape"}, {"type": "string", "value": "wide"}]}, "optional": true}, "textAlignment": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"desktop": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}, "mobile": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}}}, "optional": true}}}, "optional": true}}}}, {"name": "contentSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "contentSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "backgroundImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "position": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "top-left"}, {"type": "string", "value": "top-center"}, {"type": "string", "value": "top-right"}, {"type": "string", "value": "center-left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "center-right"}, {"type": "string", "value": "bottom-left"}, {"type": "string", "value": "bottom-center"}, {"type": "string", "value": "bottom-right"}]}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "cover"}, {"type": "string", "value": "contain"}, {"type": "string", "value": "auto"}, {"type": "string", "value": "25%"}, {"type": "string", "value": "50%"}, {"type": "string", "value": "75%"}, {"type": "string", "value": "100%"}]}, "optional": true}, "opacity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "repeat": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "no-repeat"}, {"type": "string", "value": "repeat"}, {"type": "string", "value": "repeat-x"}, {"type": "string", "value": "repeat-y"}]}, "optional": true}}}, "optional": true}, "backgroundOverlay": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"color": {"type": "objectAttribute", "value": {"type": "inline", "name": "color"}, "optional": true}, "opacity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}, "sectionItems": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featureListItem"}}}, "optional": true}, "ctaButtons": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "ctaButton"}}}, "optional": true}, "isContentCenter": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "textAlign": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "inline", "name": "paddingControls"}, "optional": true}}}}, {"name": "pageHeroSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "pageHeroSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "textAlign": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "inline", "name": "paddingControls"}, "optional": true}}}}, {"name": "aboutSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "aboutSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "layout": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "textOnly"}, {"type": "string", "value": "textWithImage"}, {"type": "string", "value": "twoColumns"}]}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "imagePosition": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "contentLeft"}, {"type": "string", "value": "contentRight"}, {"type": "string", "value": "contentCenter"}]}, "optional": true}, "ctaButtons": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "ctaButton"}}}, "optional": true}, "stats": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"number": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "textAlign": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "inline", "name": "paddingControls"}, "optional": true}}}}, {"name": "productPromotionBannerSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "productPromotionBannerSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "badge": {"type": "objectAttribute", "value": {"type": "inline", "name": "badge"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "primaryButton": {"type": "objectAttribute", "value": {"type": "inline", "name": "ctaButton"}, "optional": true}, "secondaryButton": {"type": "objectAttribute", "value": {"type": "inline", "name": "ctaButton"}, "optional": true}, "backgroundImage": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "overlay": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"enabled": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "color": {"type": "objectAttribute", "value": {"type": "inline", "name": "color"}, "optional": true}, "opacity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}, "textAlignment": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"desktop": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}, "mobile": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}}}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}, {"type": "string", "value": "fullscreen"}]}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"top": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}}}, "optional": true}, "borderRadius": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "sm"}, {"type": "string", "value": "md"}, {"type": "string", "value": "lg"}, {"type": "string", "value": "xl"}, {"type": "string", "value": "2xl"}, {"type": "string", "value": "3xl"}]}, "optional": true}}}}, {"name": "productBannerSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "productBannerSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "primaryButton": {"type": "objectAttribute", "value": {"type": "inline", "name": "ctaButton"}, "optional": true}, "secondaryButton": {"type": "objectAttribute", "value": {"type": "inline", "name": "ctaButton"}, "optional": true}, "backgroundStyle": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"type": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "solid"}, {"type": "string", "value": "gradient"}, {"type": "string", "value": "image"}]}, "optional": true}, "primaryColor": {"type": "objectAttribute", "value": {"type": "inline", "name": "color"}, "optional": true}, "secondaryColor": {"type": "objectAttribute", "value": {"type": "inline", "name": "color"}, "optional": true}, "gradientDirection": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "to-b"}, {"type": "string", "value": "to-bl"}, {"type": "string", "value": "to-l"}, {"type": "string", "value": "to-tl"}, {"type": "string", "value": "to-t"}, {"type": "string", "value": "to-tr"}, {"type": "string", "value": "to-r"}, {"type": "string", "value": "to-br"}]}, "optional": true}, "backgroundImage": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "overlay": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"enabled": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "color": {"type": "objectAttribute", "value": {"type": "inline", "name": "color"}, "optional": true}, "opacity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}}}, "optional": true}, "textAlignment": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"desktop": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}, "mobile": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "center"}, {"type": "string", "value": "right"}]}, "optional": true}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"top": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}}}, "optional": true}, "borderRadius": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "sm"}, {"type": "string", "value": "md"}, {"type": "string", "value": "lg"}, {"type": "string", "value": "xl"}, {"type": "string", "value": "2xl"}, {"type": "string", "value": "3xl"}]}, "optional": true}}}}, {"name": "productInteractiveSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "productInteractiveSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "items": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"top": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}}}, "optional": true}, "settings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"defaultActiveItem": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "animationDuration": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "fast"}, {"type": "string", "value": "normal"}, {"type": "string", "value": "slow"}]}, "optional": true}}}, "optional": true}}}}, {"name": "productFeatureSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "productFeatureSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "layout": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "imageLeft"}, {"type": "string", "value": "contentLeft"}]}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "padding": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"top": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}}}, "optional": true}, "settings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"maxWidth": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "standard"}, {"type": "string", "value": "wide"}, {"type": "string", "value": "extraWide"}, {"type": "string", "value": "full"}]}, "optional": true}, "horizontalPadding": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "standard"}, {"type": "string", "value": "large"}, {"type": "string", "value": "extraLarge"}]}, "optional": true}, "gap": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}]}, "optional": true}}}, "optional": true}}}}, {"name": "productHeroSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "productHeroSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "pillText": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "ctaButtons": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "ctaButton"}}}, "optional": true}, "heroImage": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "backgroundElements": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "position": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"top": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "sm"}, {"type": "string", "value": "md"}, {"type": "string", "value": "lg"}, {"type": "string", "value": "xl"}]}, "optional": true}, "opacity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "rotation": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "zIndex": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "settings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"fullHeight": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "centerContent": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "showScrollIndicator": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "parallaxEffect": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}, "optional": true}}}}, {"name": "heroSection", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "heroSection"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "heading": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeRichTextBlock"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "ctaButtons": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "ctaButton"}}}, "optional": true}, "heroImage": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "backgroundColor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "backgroundColor"}}}}, "optional": true}, "backgroundElements": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "position": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"top": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "sm"}, {"type": "string", "value": "md"}, {"type": "string", "value": "lg"}, {"type": "string", "value": "xl"}]}, "optional": true}, "opacity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "rotation": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "zIndex": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "settings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"fullHeight": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "centerContent": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "showScrollIndicator": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "parallaxEffect": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}, "optional": true}}}}, {"name": "badge", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "badge"}}, "text": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "color": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "primary"}, {"type": "string", "value": "success"}, {"type": "string", "value": "warning"}, {"type": "string", "value": "error"}, {"type": "string", "value": "info"}, {"type": "string", "value": "purple"}, {"type": "string", "value": "pink"}, {"type": "string", "value": "indigo"}, {"type": "string", "value": "gray"}, {"type": "string", "value": "custom"}]}, "optional": true}, "customColor": {"type": "objectAttribute", "value": {"type": "inline", "name": "color"}, "optional": true}, "variant": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "filled"}, {"type": "string", "value": "outline"}, {"type": "string", "value": "soft"}]}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "sm"}, {"type": "string", "value": "md"}, {"type": "string", "value": "lg"}]}, "optional": true}}}}, {"name": "colorField", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "colorField"}}, "preset": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "white"}, {"type": "string", "value": "gray-50"}, {"type": "string", "value": "blue-50"}, {"type": "string", "value": "green-50"}, {"type": "string", "value": "custom"}]}, "optional": true}, "custom": {"type": "objectAttribute", "value": {"type": "inline", "name": "color"}, "optional": true}, "opacity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "featureListItem", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "featureListItem"}}, "text": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "icon": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "check"}, {"type": "string", "value": "star"}, {"type": "string", "value": "arrowRight"}, {"type": "string", "value": "plus"}, {"type": "string", "value": "heart"}, {"type": "string", "value": "shield"}, {"type": "string", "value": "lightning"}, {"type": "string", "value": "globe"}, {"type": "string", "value": "cog"}, {"type": "string", "value": "user"}]}, "optional": true}, "iconColor": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "primary"}, {"type": "string", "value": "success"}, {"type": "string", "value": "warning"}, {"type": "string", "value": "error"}, {"type": "string", "value": "gray"}]}, "optional": true}, "highlighted": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "link": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "openInNewTab": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}, "optional": true}}}}, {"name": "ctaButton", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "ctaButton"}}, "text": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "variant": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "primary"}, {"type": "string", "value": "secondary"}, {"type": "string", "value": "outline"}, {"type": "string", "value": "ghost"}]}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "sm"}, {"type": "string", "value": "md"}, {"type": "string", "value": "lg"}]}, "optional": true}, "openInNewTab": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "icon": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": ""}, {"type": "string", "value": "arrowRight"}, {"type": "string", "value": "externalLink"}, {"type": "string", "value": "download"}, {"type": "string", "value": "play"}, {"type": "string", "value": "check"}, {"type": "string", "value": "star"}, {"type": "string", "value": "plus"}, {"type": "string", "value": "heart"}, {"type": "string", "value": "shield"}, {"type": "string", "value": "lightning"}, {"type": "string", "value": "globe"}, {"type": "string", "value": "cog"}, {"type": "string", "value": "user"}]}, "optional": true}, "disabled": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "richTextBlock", "type": "type", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "blank": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"color": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "primary"}, {"type": "string", "value": "secondary"}, {"type": "string", "value": "accent"}, {"type": "string", "value": "success"}, {"type": "string", "value": "warning"}, {"type": "string", "value": "error"}]}, "optional": true}, "customColor": {"type": "objectAttribute", "value": {"type": "inline", "name": "color"}, "optional": true}, "fontWeight": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "semibold"}, {"type": "string", "value": "bold"}]}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "coloredText"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "caption": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}}, {"name": "page", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "page"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "isHomePage": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "seo": {"type": "objectAttribute", "value": {"type": "inline", "name": "seoFields"}, "optional": true}, "sections": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "heroSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "productHeroSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "productFeatureSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "productInteractiveSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "productBannerSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "productPromotionBannerSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "aboutSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "pageHeroSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "contentSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featureSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "servicesSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "testimonialSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "tryCarsuBanner"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "contactFormSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "faqSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "pricingCalculatorSection"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featureCardsSection"}}]}}, "optional": true}}}, {"name": "paddingControls", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "paddingControls"}}, "top": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}}}}, {"name": "backgroundColor", "type": "type", "value": {"type": "inline", "name": "color"}}, {"name": "localeRichTextBlock", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "localeRichTextBlock"}}, "en": {"type": "objectAttribute", "value": {"type": "inline", "name": "richTextBlock"}, "optional": true}, "es": {"type": "objectAttribute", "value": {"type": "inline", "name": "richTextBlock"}, "optional": true}, "it": {"type": "objectAttribute", "value": {"type": "inline", "name": "richTextBlock"}, "optional": true}}}}, {"name": "siteLayout", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "siteLayout"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sections": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "header": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "header"}, "optional": true}, "sticky": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "transparent": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "headerSection"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "footer": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "footer"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "footerSection"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}, "seo": {"type": "objectAttribute", "value": {"type": "inline", "name": "seoFields"}, "optional": true}}}, {"name": "footer", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "footer"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "logo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "richTextBlock"}, "optional": true}, "columns": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "links": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "openInNewTab": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "socialLinks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"platform": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "facebook"}, {"type": "string", "value": "twitter"}, {"type": "string", "value": "instagram"}, {"type": "string", "value": "linkedin"}, {"type": "string", "value": "youtube"}, {"type": "string", "value": "github"}, {"type": "string", "value": "discord"}, {"type": "string", "value": "tiktok"}]}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "copyrightText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "showBackToTop": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "newsletter": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"enabled": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "placeholder": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "buttonText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}}}, {"name": "header", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "header"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "logo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}, "navigation": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "hasDropdown": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "dropdownLayout": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"columns": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "number", "value": 1}, {"type": "number", "value": 2}, {"type": "number", "value": 3}, {"type": "number", "value": 4}]}, "optional": true}, "showImages": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "small"}, {"type": "string", "value": "medium"}, {"type": "string", "value": "large"}, {"type": "string", "value": "xl"}]}, "optional": true}}}, "optional": true}, "dropdownItems": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "inline", "name": "imageWithAlt"}, "optional": true}, "badge": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"text": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "color": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "blue"}, {"type": "string", "value": "green"}, {"type": "string", "value": "yellow"}, {"type": "string", "value": "red"}, {"type": "string", "value": "purple"}]}, "optional": true}}}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "ctaButtons": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "ctaButton"}}}, "optional": true}, "mobileSettings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"showCTAInMobile": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "mobileMenuPosition": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "below"}, {"type": "string", "value": "overlay"}]}, "optional": true}}}, "optional": true}}}, {"name": "imageWithAlt", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageWithAlt"}}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "caption": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "priority": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "testimonial", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "testimonial"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "company": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "quote": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "avatar": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "rating": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "number", "value": 5}, {"type": "number", "value": 4}, {"type": "number", "value": 3}, {"type": "number", "value": 2}, {"type": "number", "value": 1}]}, "optional": true}, "featured": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "order": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, {"name": "category", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "category"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "color": {"type": "objectAttribute", "value": {"type": "inline", "name": "color"}, "optional": true}, "parent": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "category"}, "optional": true}, "featured": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "order": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "seo": {"type": "objectAttribute", "value": {"type": "inline", "name": "seoFields"}, "optional": true}}}, {"name": "post", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "post"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "author": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "author"}, "optional": true}, "mainImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "excerpt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "categories": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "category", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "tags": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "publishedAt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "featured": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "body": {"type": "objectAttribute", "value": {"type": "inline", "name": "richTextBlock"}, "optional": true}, "seo": {"type": "objectAttribute", "value": {"type": "inline", "name": "seoFields"}, "optional": true}}}, {"name": "author", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "author"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "company": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "bio": {"type": "objectAttribute", "value": {"type": "inline", "name": "richTextBlock"}, "optional": true}, "email": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "website": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "socialLinks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"platform": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "twitter"}, {"type": "string", "value": "linkedin"}, {"type": "string", "value": "github"}, {"type": "string", "value": "instagram"}, {"type": "string", "value": "facebook"}]}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "featured": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "seo": {"type": "objectAttribute", "value": {"type": "inline", "name": "seoFields"}, "optional": true}}}, {"name": "seoFields", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "seoFields"}}, "metaTitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "metaDescription": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "ogTitle": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "ogDescription": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "ogImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "inline", "name": "localeString"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "noIndex": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "noFollow": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "canonicalUrl": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "keywords": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}}}}, {"name": "localeString", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "localeString"}}, "en": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "es": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "it": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "color", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "color"}}, "hex": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "alpha": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "hsl": {"type": "objectAttribute", "value": {"type": "inline", "name": "hslaColor"}, "optional": true}, "hsv": {"type": "objectAttribute", "value": {"type": "inline", "name": "hsvaColor"}, "optional": true}, "rgb": {"type": "objectAttribute", "value": {"type": "inline", "name": "rgbaColor"}, "optional": true}}}}, {"name": "rgbaColor", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rgbaColor"}}, "r": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "g": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "b": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "a": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "hsvaColor", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "hsvaColor"}}, "h": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "s": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "v": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "a": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "hslaColor", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "hslaColor"}}, "h": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "s": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "l": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "a": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imagePaletteSwatch", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePaletteSwatch"}}, "background": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "foreground": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "population": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.imagePalette", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePalette"}}, "darkMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "darkVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "vibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "dominant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "muted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}}}}, {"name": "sanity.imageDimensions", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageDimensions"}}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "aspectRatio": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageHotspot", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageHotspot"}}, "x": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "y": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageCrop", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageCrop"}}, "top": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.fileAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.fileAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "metadata": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageMetadata"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageMetadata", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageMetadata"}}, "location": {"type": "objectAttribute", "value": {"type": "inline", "name": "geopoint"}, "optional": true}, "dimensions": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageDimensions"}, "optional": true}, "palette": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePalette"}, "optional": true}, "lqip": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "blurHash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "hasAlpha": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isOpaque": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "geopoint", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "geopoint"}}, "lat": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "lng": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "slug", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "slug"}}, "current": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assetSourceData", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assetSourceData"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "id": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}]