@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@import "tailwindcss";
@plugin "@tailwindcss/typography";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@view-transition {
  navigation: auto;
}

::view-transition-old(content) {
  animation: slide-out 0.5s cubic-bezier(0.86, 0, 0.07, 1) forwards;
}

::view-transition-new(content) {
  animation: slide-in 0.5s cubic-bezier(0.86, 0, 0.07, 1) forwards;
}

@keyframes slide-out {
  0% {
    transform: translateX(0%);
  }

  100% {
    opacity: 0;
    transform: translateX(calc(50% * var(--direction)));
  }
}

@keyframes slide-in {
  0% {
    opacity: 0;
    transform: translateX(calc(-50% * var(--direction)));
  }

  100% {
    transform: translateX(0%);
  }
}

:root {
  --direction: -1;
}

@media screen and (prefers-reduced-motion: no-preference) {
  @view-transition {
    navigation: auto;
  }
}


* {
  
  box-sizing: border-box;
}

h1,h2, h3, h4, h5, h6 {
  font-family: "Poppins", sans-serif;
}

div, span, a, p, button {
  font-family: "DM Sans", sans-serif;
}


p {
  color: #4D525E !important;
}