import { UserIcon } from '@sanity/icons';
import { defineField, defineType } from 'sanity';

export const authorType = defineType({
  name: 'author',
  title: 'Author',
  type: 'document',
  icon: UserIcon,
  fields: [
    defineField({
      name: 'name',
      title: 'Full Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'image',
      title: 'Profile Image',
      type: 'image',
      options: {
        hotspot: true,
      },
      fields: [
        defineField({
          name: 'alt',
          title: 'Alt Text',
          type: 'string',
          validation: (Rule) => Rule.required(),
        }),
      ],
    }),
    defineField({
      name: 'title',
      title: 'Job Title',
      type: 'string',
      description: 'e.g., "Senior Developer", "Content Writer"',
    }),
    defineField({
      name: 'company',
      title: 'Company',
      type: 'string',
    }),
    define<PERSON>ield({
      name: 'bio',
      title: 'Biography',
      type: 'richTextBlock',
      description: 'Author biography and background',
    }),
    define<PERSON>ield({
      name: 'email',
      title: 'Email',
      type: 'email',
      description: 'Contact email (not displayed publicly)',
    }),
    defineField({
      name: 'website',
      title: 'Website',
      type: 'url',
      description: 'Personal or professional website',
    }),
    defineField({
      name: 'socialLinks',
      title: 'Social Media Links',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'platform',
              title: 'Platform',
              type: 'string',
              options: {
                list: [
                  { title: 'Twitter', value: 'twitter' },
                  { title: 'LinkedIn', value: 'linkedin' },
                  { title: 'GitHub', value: 'github' },
                  { title: 'Instagram', value: 'instagram' },
                  { title: 'Facebook', value: 'facebook' },
                ],
              },
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'url',
              title: 'Profile URL',
              type: 'url',
              validation: (Rule) => Rule.required(),
            }),
          ],
          preview: {
            select: {
              platform: 'platform',
              url: 'url',
            },
            prepare({ platform, url }) {
              return {
                title: platform,
                subtitle: url,
              };
            },
          },
        },
      ],
      validation: (Rule) => Rule.max(5),
    }),
    defineField({
      name: 'featured',
      title: 'Featured Author',
      type: 'boolean',
      initialValue: false,
      description: 'Display prominently on author pages',
    }),
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'seoFields',
    }),
  ],
  preview: {
    select: {
      name: 'name',
      title: 'title',
      media: 'image',
      featured: 'featured',
    },
    prepare({ name, title, media, featured }) {
      return {
        title: name || 'Unnamed Author',
        subtitle: `${title || 'No title'}${featured ? ' • Featured' : ''}`,
        media,
      };
    },
  },
});
